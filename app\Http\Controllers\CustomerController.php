<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\CustomerPhone;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CustomerController extends Controller
{
    public function index()
    {
        return view('customers.index');
    }

    public function datatable()
    {
        $query = \App\Models\Customer::with(['phones', 'authorizedPersons']);

        // DataTables parametrelerini al
        $draw = request('draw');
        $start = request('start', 0);
        $length = request('length', 10);
        $searchValue = request('search.value');
        $orderColumn = request('order.0.column', 0);
        $orderDir = request('order.0.dir', 'desc');

        // Sütun isimleri
        $columns = ['id', 'email', 'authorized_title', 'authorized_first_name', 'authorized_last_name', 'authorized_phone', 'company_name', 'phones', 'city', 'district', 'created_at'];

        // Genel arama
        if ($searchValue) {
            $query->where(function($q) use ($searchValue) {
                $q->where('email', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_title', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_first_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_last_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_phone', 'ilike', "%{$searchValue}%")
                  ->orWhere('company_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('city', 'ilike', "%{$searchValue}%")
                  ->orWhere('district', 'ilike', "%{$searchValue}%")
                  ->orWhere('address', 'ilike', "%{$searchValue}%");
            });
        }

        // Sütun bazlı arama
        for ($i = 0; $i < count($columns); $i++) {
            $columnSearch = request("columns.{$i}.search.value");
            if ($columnSearch && isset($columns[$i])) {
                $column = $columns[$i];
                if ($column !== 'phones') {
                    $query->where($column, 'ilike', "%{$columnSearch}%");
                }
            }
        }

        // Toplam kayıt sayısı
        $totalRecords = \App\Models\Customer::count();
        $filteredRecords = $query->count();

        // Sıralama
        if (isset($columns[$orderColumn])) {
            $query->orderBy($columns[$orderColumn], $orderDir);
        }

        // Sayfalama
        $customers = $query->skip($start)->take($length)->get();

        // Veriyi formatla
        $data = [];
        foreach ($customers as $customer) {
            // Telefon bilgilerini topla
            $allPhones = collect();
            if ($customer->phones && $customer->phones->count() > 0) {
                foreach ($customer->phones as $phone) {
                    $allPhones->push($phone->phone . ' (' . $phone->type . ')');
                }
            }
            $phonesText = $allPhones->count() > 0 ? $allPhones->implode('<br>') : '<small class="text-muted">Telefon yok</small>';

            $data[] = [
                $customer->id,
                $customer->email,
                $customer->authorized_title ?? '-',
                $customer->authorized_first_name ?? '-',
                $customer->authorized_last_name ?? '-',
                $customer->authorized_phone ?? '-',
                $customer->company_name ?? '-',
                $phonesText,
                $customer->city ?? '-',
                $customer->district ?? '-',
                $customer->created_at ? $customer->created_at->format('d.m.Y') : '-',
                '<a href="' . route('customers.show', $customer->id) . '" class="btn btn-info btn-sm" title="Detay"><i class="fas fa-eye"></i></a> ' .
                '<a href="' . route('customers.edit', $customer->id) . '" class="btn btn-warning btn-sm" title="Düzenle"><i class="fas fa-edit"></i></a> ' .
                '<a href="' . route('customers.customer-followups.index', $customer->id) . '" class="btn btn-secondary btn-sm" title="Takip Formu"><i class="fas fa-clipboard-list"></i> Takip</a>'
            ];
        }

        return response()->json([
            'draw' => intval($draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    public function create()
    {
        $dealers = \App\Models\Dealer::with('region')->active()->orderBy('name')->get();
        return view('customers.create', compact('dealers'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([

            'email' => 'required|email|max:100|unique:customers,email',
            'tc' => 'nullable|string|max:11|unique:customers,tc',
            'pluscard_no' => 'nullable|string|max:40|unique:customers,pluscard_no',
            'authorized_title' => 'nullable|string|min:2|max:255',
            'authorized_first_name' => 'nullable|string|min:2|max:255',
            'authorized_last_name' => 'nullable|string|min:2|max:255',
            'authorized_phone' => 'nullable|string|min:10|max:20',
            'authorized_persons' => 'nullable|array',
            'authorized_persons.*.title' => 'nullable|string|max:255',
            'authorized_persons.*.first_name' => 'nullable|string|max:255',
            'authorized_persons.*.last_name' => 'nullable|string|max:255',
            'authorized_persons.*.phone' => 'nullable|string|max:20',
            'company_phones' => 'nullable|array',
            'company_phones.*.phone' => 'nullable|string|max:20',
            'company_phones.*.type' => 'nullable|string|in:Sabit,Mobil,Fax,Diğer',
            'company_name' => 'nullable|string|max:255',
            'city' => 'required|string|min:2|max:50',
            'district' => 'required|string|min:2|max:50',
            'address' => 'required|string|min:2|max:255',
            'dealer_id' => 'nullable|exists:dealers,id',
            'website_url' => 'nullable|url|max:255',
            'google_maps_url' => 'nullable|url|max:255',
            'listing_url' => 'nullable|url|max:255',
        ]);

        $authorizedPersons = $validated['authorized_persons'] ?? [];
        $companyPhones = $validated['company_phones'] ?? [];
        unset($validated['authorized_persons']);
        unset($validated['company_phones']);

        $customer = \App\Models\Customer::create($validated);

        // Yetkili kişileri ekle
        foreach ($authorizedPersons as $person) {
            if (!empty(trim($person['first_name'] ?? '')) ||
                !empty(trim($person['last_name'] ?? '')) ||
                !empty(trim($person['title'] ?? '')) ||
                !empty(trim($person['phone'] ?? ''))) {

                $customer->authorizedPersons()->create([
                    'title' => trim($person['title'] ?? ''),
                    'first_name' => trim($person['first_name'] ?? ''),
                    'last_name' => trim($person['last_name'] ?? ''),
                    'phone' => trim($person['phone'] ?? ''),
                ]);
            }
        }

        // Şirket telefonlarını ekle
        foreach ($companyPhones as $phone) {
            if (!empty(trim($phone['phone'] ?? ''))) {
                $customer->phones()->create([
                    'phone' => trim($phone['phone']),
                    'type' => trim($phone['type'] ?? 'Sabit'),
                ]);
            }
        }

        return redirect()->route('customers.index')->with('success', 'Müşteri başarıyla eklendi!');
    }

    public function show($id)
    {
        $customer = \App\Models\Customer::with(['phones', 'authorizedPersons'])->findOrFail($id);
        return view('customers.show', compact('customer'));
    }

    public function edit($id)
    {
        $customer = \App\Models\Customer::with(['phones', 'authorizedPersons', 'dealer'])->findOrFail($id);
        $dealers = \App\Models\Dealer::with('region')->active()->orderBy('name')->get();
        return view('customers.edit', compact('customer', 'dealers'));
    }

    public function update(Request $request, $id)
    {
        $customer = \App\Models\Customer::findOrFail($id);
        $validated = $request->validate([
            'email' => 'required|email|max:100|unique:customers,email,' . $customer->id,
            'tc' => 'nullable|string|max:11|unique:customers,tc,' . $customer->id,
            'pluscard_no' => 'nullable|string|max:40|unique:customers,pluscard_no,' . $customer->id,
            'authorized_title' => 'nullable|string|min:2|max:255',
            'authorized_first_name' => 'nullable|string|min:2|max:255',
            'authorized_last_name' => 'nullable|string|min:2|max:255',
            'authorized_phone' => 'nullable|string|min:10|max:20',
            'authorized_persons' => 'nullable|array',
            'authorized_persons.*.title' => 'nullable|string|max:255',
            'authorized_persons.*.first_name' => 'nullable|string|max:255',
            'authorized_persons.*.last_name' => 'nullable|string|max:255',
            'authorized_persons.*.phone' => 'nullable|string|max:20',
            'company_phones' => 'nullable|array',
            'company_phones.*.phone' => 'nullable|string|max:20',
            'company_phones.*.type' => 'nullable|string|in:Sabit,Mobil,Fax,Diğer',
            'company_phones.*.id' => 'nullable|integer|exists:customer_phones,id',
            'company_name' => 'nullable|string|max:255',
            'city' => 'required|string|min:2|max:50',
            'district' => 'required|string|min:2|max:50',
            'address' => 'required|string|min:2|max:255',
            'dealer_id' => 'nullable|exists:dealers,id',
            'website_url' => 'nullable|url|max:255',
            'google_maps_url' => 'nullable|url|max:255',
            'listing_url' => 'nullable|url|max:255',
        ]);

        $authorizedPersons = $validated['authorized_persons'] ?? [];
        $companyPhones = $validated['company_phones'] ?? [];
        unset($validated['authorized_persons']);
        unset($validated['company_phones']);

        $customer->update($validated);

        // Mevcut yetkili kişileri sil ve yenilerini ekle
        $customer->authorizedPersons()->delete();

        foreach ($authorizedPersons as $person) {
            if (!empty(trim($person['first_name'] ?? '')) ||
                !empty(trim($person['last_name'] ?? '')) ||
                !empty(trim($person['title'] ?? '')) ||
                !empty(trim($person['phone'] ?? ''))) {

                $customer->authorizedPersons()->create([
                    'title' => trim($person['title'] ?? ''),
                    'first_name' => trim($person['first_name'] ?? ''),
                    'last_name' => trim($person['last_name'] ?? ''),
                    'phone' => trim($person['phone'] ?? ''),
                ]);
            }
        }

        // Şirket telefonlarını güncelle
        $existingPhoneIds = [];
        foreach ($companyPhones as $phone) {
            if (!empty(trim($phone['phone'] ?? ''))) {
                if (!empty($phone['id'])) {
                    // Mevcut telefonu güncelle
                    $existingPhone = $customer->phones()->find($phone['id']);
                    if ($existingPhone) {
                        $existingPhone->update([
                            'phone' => trim($phone['phone']),
                            'type' => trim($phone['type'] ?? 'Sabit'),
                        ]);
                        $existingPhoneIds[] = $phone['id'];
                    }
                } else {
                    // Yeni telefon ekle
                    $newPhone = $customer->phones()->create([
                        'phone' => trim($phone['phone']),
                        'type' => trim($phone['type'] ?? 'Sabit'),
                    ]);
                    $existingPhoneIds[] = $newPhone->id;
                }
            }
        }

        // Formda olmayan telefonları sil
        $customer->phones()->whereNotIn('id', $existingPhoneIds)->delete();

        return redirect()->route('customers.index')->with('success', 'Müşteri başarıyla güncellendi!');
    }

    public function createFromPotential($potentialCustomerId)
    {
        $potential = \App\Models\PotentialCustomer::with(['authorizedPersons', 'phones'])->findOrFail($potentialCustomerId);

        // Ana alan eşleştirmesi
        $prefill = [
            'email' => $potential->email ?? '',
            'company_name' => $potential->company_name ?? '',
            'pluscard_no' => $potential->pluscard_no ?? '',
            'tc' => $potential->tc ?? '',
            'authorized_title' => $potential->authorized_title ?? '',
            'authorized_first_name' => $potential->authorized_name ?? $potential->authorized_first_name,
            'authorized_last_name' => $potential->authorized_lastname ?? $potential->authorized_last_name,
            'authorized_phone' => $potential->authorized_phone ?? '',
            'city' => $potential->city ?? '',
            'district' => $potential->district ?? '',
            'address' => $potential->address ?? '',
        ];

        // Yetkili kişileri hazırla (PotentialCustomer'da name/lastname, Customer'da first_name/last_name)
        $authorizedPersons = [];
        foreach ($potential->authorizedPersons as $person) {
            $authorizedPersons[] = [
                'title' => $person->title ?? '',
                'first_name' => $person->name ?? '', // name -> first_name
                'last_name' => $person->lastname ?? '', // lastname -> last_name
                'phone' => $person->phone ?? '',
            ];
        }

        // Şirket telefonlarını hazırla
        $companyPhones = [];
        foreach ($potential->phones as $phone) {
            $companyPhones[] = [
                'phone' => $phone->phone ?? '',
                'type' => $phone->type ?? 'Sabit',
            ];
        }

        return view('customers.create', compact('prefill', 'authorizedPersons', 'companyPhones'));
    }

    public function registrySearch(Request $request)
    {
        $q = trim($request->input('q'));

        // Telefon numarasından boşlukları ve özel karakterleri temizle
        $qNoSpace = preg_replace('/[^0-9+]/', '', $q);

        $customer = Customer::with(['phones', 'authorizedPersons'])
            ->where(function($query) use ($q, $qNoSpace) {
                $query->where('tc', $q)
                    ->orWhere('pluscard_no', $q)
                    // Yetkili telefon alanında ara
                    ->orWhere('authorized_phone', 'LIKE', "%{$q}%")
                    ->orWhereRaw("REPLACE(REPLACE(REPLACE(authorized_phone, ' ', ''), '-', ''), '(', '') LIKE ?", ["%{$qNoSpace}%"])
                    // Şirket telefonlarında ara
                    ->orWhereHas('phones', function($phoneQuery) use ($q, $qNoSpace) {
                        $phoneQuery->where('phone', 'LIKE', "%{$q}%")
                            ->orWhereRaw("REPLACE(REPLACE(REPLACE(phone, ' ', ''), '-', ''), '(', '') LIKE ?", ["%{$qNoSpace}%"]);
                    })
                    // Yetkili kişi telefonlarında ara
                    ->orWhereHas('authorizedPersons', function($authorizedQuery) use ($q, $qNoSpace) {
                        $authorizedQuery->where('phone', 'LIKE', "%{$q}%")
                            ->orWhereRaw("REPLACE(REPLACE(REPLACE(phone, ' ', ''), '-', ''), '(', '') LIKE ?", ["%{$qNoSpace}%"]);
                    });
            })
            ->first();

        if ($customer) {
            // Müşteri bulundu, telefon bilgilerini de dahil ederek döndür
            $customerData = $customer->toArray();

            // Şirket telefonlarını ekle
            $customerData['company_phones'] = $customer->phones->map(function($phone) {
                return [
                    'phone' => $phone->phone,
                    'type' => $phone->type
                ];
            })->toArray();

            // Yetkili kişileri ekle
            $customerData['authorized_persons'] = $customer->authorizedPersons->map(function($person) {
                return [
                    'title' => $person->title,
                    'first_name' => $person->first_name,
                    'last_name' => $person->last_name,
                    'phone' => $person->phone
                ];
            })->toArray();

            return response()->json($customerData);
        } else {
            return response()->json(['message' => 'Kayıt bulunamadı'], 404);
        }
    }

    public function exportExcel(Request $request)
    {
        $query = Customer::with(['phones', 'authorizedPersons']);

        // Filtre parametrelerini al
        $searchValue = $request->get('search');

        // Genel arama
        if ($searchValue) {
            $query->where(function($q) use ($searchValue) {
                $q->where('email', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_title', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_first_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_last_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('authorized_phone', 'ilike', "%{$searchValue}%")
                  ->orWhere('company_name', 'ilike', "%{$searchValue}%")
                  ->orWhere('city', 'ilike', "%{$searchValue}%")
                  ->orWhere('district', 'ilike', "%{$searchValue}%");
            });
        }

        // Sütun bazlı filtreler
        if ($request->has('columns')) {
            foreach ($request->get('columns') as $index => $column) {
                if (!empty($column['search']['value'])) {
                    $searchTerm = $column['search']['value'];
                    switch ($index) {
                        case 0: // ID
                            $query->where('id', 'like', "%{$searchTerm}%");
                            break;
                        case 1: // Email
                            $query->where('email', 'ilike', "%{$searchTerm}%");
                            break;
                        case 2: // Authorized Title
                            $query->where('authorized_title', 'ilike', "%{$searchTerm}%");
                            break;
                        case 3: // Authorized First Name
                            $query->where('authorized_first_name', 'ilike', "%{$searchTerm}%");
                            break;
                        case 4: // Authorized Last Name
                            $query->where('authorized_last_name', 'ilike', "%{$searchTerm}%");
                            break;
                        case 5: // Authorized Phone
                            $query->where('authorized_phone', 'ilike', "%{$searchTerm}%");
                            break;
                        case 6: // Company Name
                            $query->where('company_name', 'ilike', "%{$searchTerm}%");
                            break;
                        case 8: // City
                            $query->where('city', 'ilike', "%{$searchTerm}%");
                            break;
                        case 9: // District
                            $query->where('district', 'ilike', "%{$searchTerm}%");
                            break;
                    }
                }
            }
        }

        $customers = $query->get();

        $filename = 'musteriler_' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($customers) {
            $file = fopen('php://output', 'w');

            // UTF-8 BOM ekle
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // Header satırı
            fputcsv($file, [
                'ID',
                'Email',
                'Yetkili Unvan',
                'Yetkili İsim',
                'Yetkili Soyisim',
                'Yetkili Telefon',
                'Şirket İsmi',
                'Şirket Telefonları',
                'İl',
                'İlçe',
                'Kayıt Tarihi'
            ]);

            // Veri satırları
            foreach ($customers as $customer) {
                $phones = $customer->phones->pluck('phone')->implode(', ');

                fputcsv($file, [
                    $customer->id,
                    $customer->email,
                    $customer->authorized_title ?? '-',
                    $customer->authorized_first_name ?? '-',
                    $customer->authorized_last_name ?? '-',
                    $customer->authorized_phone ?? '-',
                    $customer->company_name ?? '-',
                    $phones ?: '-',
                    $customer->city ?? '-',
                    $customer->district ?? '-',
                    $customer->created_at ? $customer->created_at->format('d.m.Y') : '-'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
    
}