<?php $__env->startSection('content'); ?>
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title"><PERSON><PERSON></h3>
                </div>
                <div class="card-body">
                    
                <form id="customer-search-form" method="GET" action="" class="mb-3 d-flex" style="gap: 8px;">
                        <div class="input-group">
                            <input type="text" id="customer-search-input" name="q" class="form-control" placeholder="TC ya da Telefon" value="<?php echo e(request('q')); ?>">
                            <button class="btn btn-outline-secondary" type="submit">Ara</button>
                        </div>
                    </form>
                    
                    <form action="<?php echo e(route('customers.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        
                        <!-- <PERSON><PERSON> Bilgileri -->
                        <!--
                        <div class="row">  
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="pluscard_no" class="form-label">Pluscard No</label>
                                    <input type="text" class="form-control" id="pluscard_no" name="pluscard_no" value="<?php echo e(old('pluscard_no', $prefill['pluscard_no'] ?? '')); ?>" placeholder="Pluscard numarası">
                                    <?php $__errorArgs = ['pluscard_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                         -->

                        <!-- Firma Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Firma Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="company_name" class="form-label">Firma Adı <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" value="<?php echo e(old('company_name', $prefill['company_name'] ?? '')); ?>" required placeholder="Firma adı">
                                    <?php $__errorArgs = ['company_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="email" class="form-label">E-posta <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo e(old('email', $prefill['email'] ?? '')); ?>" required placeholder="<EMAIL>">
                                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="website_url" class="form-label">Web Sitesi</label>
                                    <input type="url" class="form-control" id="website_url" name="website_url" value="<?php echo e(old('website_url')); ?>" placeholder="https://example.com">
                                    <?php $__errorArgs = ['website_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="google_maps_url" class="form-label">Google Harita Linki</label>
                                    <input type="url" class="form-control" id="google_maps_url" name="google_maps_url" value="<?php echo e(old('google_maps_url')); ?>" placeholder="https://maps.google.com/...">
                                    <?php $__errorArgs = ['google_maps_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="listing_url" class="form-label">İlan Sitesi Linki</label>
                                    <input type="url" class="form-control" id="listing_url" name="listing_url" value="<?php echo e(old('listing_url')); ?>" placeholder="https://ilan.example.com/...">
                                    <?php $__errorArgs = ['listing_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="dealer_id" class="form-label">Bayi</label>
                                    <select class="form-control" id="dealer_id" name="dealer_id">
                                        <option value="">Bayi Seçin</option>
                                        <?php $__currentLoopData = $dealers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dealer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($dealer->id); ?>" <?php echo e(old('dealer_id') == $dealer->id ? 'selected' : ''); ?>>
                                                <?php echo e($dealer->name); ?> (<?php echo e($dealer->region->name ?? 'Bölge Yok'); ?>)
                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['dealer_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Yetkili Kişi Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Yetkili Kişi Bilgileri</h5>

                        <!-- Eski tek yetkili kişi alanları (geriye dönük uyumluluk için) -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="authorized_title" class="form-label">Yetkili Ünvan</label>
                                    <input type="text" class="form-control" id="authorized_title" name="authorized_title" value="<?php echo e(old('authorized_title', $prefill['authorized_title'] ?? '')); ?>" placeholder="Örn: Genel Müdür, Satış Müdürü">
                                    <?php $__errorArgs = ['authorized_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="authorized_phone" class="form-label">Yetkili Telefon</label>
                                    <input type="text" class="form-control inputmask" id="authorized_phone" name="authorized_phone" value="<?php echo e(old('authorized_phone', $prefill['authorized_phone'] ?? '')); ?>" data-inputmask="'mask': '+99 999 999 99 99'" inputmode="tel" pattern="\+[0-9 ]{13,16}" placeholder="+90 5xx xxx xx xx">
                                    <?php $__errorArgs = ['authorized_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="authorized_first_name" class="form-label">Yetkili İsim</label>
                                    <input type="text" class="form-control" id="authorized_first_name" name="authorized_first_name" value="<?php echo e(old('authorized_first_name', $prefill['authorized_first_name'] ?? '')); ?>" placeholder="Ad">
                                    <?php $__errorArgs = ['authorized_first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="authorized_last_name" class="form-label">Yetkili Soyisim</label>
                                    <input type="text" class="form-control" id="authorized_last_name" name="authorized_last_name" value="<?php echo e(old('authorized_last_name', $prefill['authorized_last_name'] ?? '')); ?>" placeholder="Soyad">
                                    <?php $__errorArgs = ['authorized_last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <!-- Yeni çoklu yetkili kişi sistemi -->
                        <h6 class="mb-3 text-secondary mt-4">Ek Yetkili Kişiler</h6>
                        <div class="mb-3">
                            <button type="button" id="add-authorized-person" class="btn btn-sm btn-success">
                                <i class="fas fa-plus"></i> Yeni Yetkili Ekle
                            </button>
                        </div>

                        <div id="authorized-persons-container">
                            <!-- Dinamik yetkili kişi alanları buraya eklenecek -->
                        </div>
                        
                        <!-- İletişim Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">İletişim Bilgileri</h5>

                        <!-- Şirket Telefonları -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Şirket Telefonları</h6>
                                <button type="button" class="btn btn-sm btn-primary" id="add-company-phone">
                                    <i class="fas fa-plus"></i> Telefon Ekle
                                </button>
                            </div>

                            <div id="company-phones-container">
                                <!-- İlk telefon alanı varsayılan olarak gösterilir -->
                                <div class="company-phone-item border p-3 mb-3 rounded" data-index="0">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">Şirket Telefon #1</h6>
                                        <button type="button" class="btn btn-sm btn-danger remove-company-phone" style="display: none;">
                                            <i class="fas fa-trash"></i> Kaldır
                                        </button>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Telefon Numarası</label>
                                                <input type="text" class="form-control inputmask" name="company_phones[0][phone]" value="<?php echo e(old('company_phones.0.phone', isset($companyPhones[0]['phone']) ? $companyPhones[0]['phone'] : ($prefill['phone_1'] ?? ''))); ?>" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 2xx xxx xx xx">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label class="form-label">Telefon Türü</label>
                                                <select class="form-control" name="company_phones[0][type]">
                                                    <?php
                                                        $firstPhoneType = old('company_phones.0.type', isset($companyPhones[0]['type']) ? $companyPhones[0]['type'] : 'Sabit');
                                                    ?>
                                                    <option value="Sabit" <?php echo e($firstPhoneType == 'Sabit' ? 'selected' : ''); ?>>Sabit Hat</option>
                                                    <option value="Mobil" <?php echo e($firstPhoneType == 'Mobil' ? 'selected' : ''); ?>>Mobil</option>
                                                    <option value="Fax" <?php echo e($firstPhoneType == 'Fax' ? 'selected' : ''); ?>>Fax</option>
                                                    <option value="Diğer" <?php echo e($firstPhoneType == 'Diğer' ? 'selected' : ''); ?>>Diğer</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Adres Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Adres Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="city" class="form-label">İl <span class="text-danger">*</span></label>
                                    <select class="form-control" id="city" name="city" required>
                                        <option value="">İl seçiniz</option>
                                    </select>
                                    <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="district" class="form-label">İlçe <span class="text-danger">*</span></label>
                                    <select class="form-control" id="district" name="district" required disabled>
                                        <option value="">İlçe seçiniz</option>
                                    </select>
                                    <?php $__errorArgs = ['district'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="address" class="form-label">Adres <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required placeholder="Detaylı adres bilgisi"><?php echo e(old('address', $prefill['address'] ?? '')); ?></textarea>
                                    <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer d-flex flex-column gap-2">
                            <button type="submit" class="btn btn-success w-100">Kaydet</button>
                            <a href="<?php echo e(route('customers.index')); ?>" class="btn btn-secondary w-100">İptal</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script src="<?php echo e(asset('assets')); ?>/plugins/inputmask/jquery.inputmask.bundle.js"></script>
    <script>
        // Türkiye il-ilçe verisini harici JSON'dan yükle
        let cityDistricts = {};
        $(document).ready(function(){
            // AJAX ile müşteri arama ve form doldurma
            $('#customer-search-form').on('submit', function(e) {
                e.preventDefault();
                let q = $('#customer-search-input').val();
                if (!q) return;

                // Arama sırasında loading göster
                $('#customer-search-input').prop('disabled', true);
                $('button[type="submit"]', this).prop('disabled', true).text('Arıyor...');

                $.ajax({
                    url: '<?php echo e(route('customers.registrySearch')); ?>',
                    method: 'GET',
                    data: { q: q },
                    success: function(data) {
                        // Temel müşteri bilgilerini doldur
                        $('#name').val(data.name);
                        $('#lastname').val(data.lastname);
                        $('#tc').val(data.tc);
                        $('#pluscard_no').val(data.pluscard_no);
                        $('#email').val(data.email);
                        $('#authorized_title').val(data.authorized_title);
                        $('#authorized_first_name').val(data.authorized_first_name);
                        $('#authorized_last_name').val(data.authorized_last_name);
                        $('#authorized_phone').val(data.authorized_phone);
                        $('#company_name').val(data.company_name);
                        $('#city').val(data.city).trigger('change');
                        setTimeout(function() {
                            $('#district').val(data.district);
                        }, 200);
                        $('#address').val(data.address);

                        // Şirket telefonlarını doldur
                        if (data.company_phones && data.company_phones.length > 0) {
                            // İlk telefonu mevcut alana yerleştir
                            if (data.company_phones[0]) {
                                $('input[name="company_phones[0][phone]"]').val(data.company_phones[0].phone);
                                $('select[name="company_phones[0][type]"]').val(data.company_phones[0].type);
                            }

                            // Ek telefonları ekle
                            for (let i = 1; i < data.company_phones.length; i++) {
                                const phone = data.company_phones[i];
                                if (phone.phone) {
                                    // Yeni telefon alanı ekle
                                    $('#add-company-phone').click();

                                    // Son eklenen telefon alanını doldur
                                    setTimeout(function() {
                                        const lastPhoneItem = $('.company-phone-item').last();
                                        lastPhoneItem.find('input[name*="[phone]"]').val(phone.phone);
                                        lastPhoneItem.find('select[name*="[type]"]').val(phone.type);
                                    }, 100);
                                }
                            }
                        }

                        // Yetkili kişileri doldur
                        if (data.authorized_persons && data.authorized_persons.length > 0) {
                            for (let i = 0; i < data.authorized_persons.length; i++) {
                                const person = data.authorized_persons[i];
                                if (person.first_name || person.last_name || person.title || person.phone) {
                                    // Yeni yetkili kişi alanı ekle
                                    $('#add-authorized-person').click();

                                    // Son eklenen yetkili kişi alanını doldur
                                    setTimeout(function() {
                                        const lastPersonItem = $('.authorized-person-item').last();
                                        lastPersonItem.find('input[name*="[title]"]').val(person.title || '');
                                        lastPersonItem.find('input[name*="[first_name]"]').val(person.first_name || '');
                                        lastPersonItem.find('input[name*="[last_name]"]').val(person.last_name || '');
                                        lastPersonItem.find('input[name*="[phone]"]').val(person.phone || '');
                                    }, 100 * (i + 1));
                                }
                            }
                        }

                        alert('Müşteri bilgileri başarıyla yüklendi!');
                    },
                    error: function(xhr) {
                        if (xhr.status === 404) {
                            alert('Bu telefon numarası ile kayıtlı müşteri bulunamadı.');
                        } else {
                            alert('Arama sırasında bir hata oluştu.');
                        }
                    },
                    complete: function() {
                        // Loading durumunu kaldır
                        $('#customer-search-input').prop('disabled', false);
                        $('button[type="submit"]', '#customer-search-form').prop('disabled', false).text('Ara');
                    }
                });
            });
            const citySelect = $('#city');
            const districtSelect = $('#district');
            fetch('<?php echo e(asset('assets')); ?>/turkiye-il-ilce.json')
                .then(response => response.json())
                .then(data => {
                    cityDistricts = data;
                    Object.keys(cityDistricts).forEach(function(city) {
                        citySelect.append(`<option value="${city}">${city}</option>`);
                    });
                    // Eğer eski değer varsa otomatik doldur
                    const oldCity = "<?php echo e(old('city')); ?>";
                    const oldDistrict = "<?php echo e(old('district')); ?>";
                    if (oldCity) {
                        citySelect.val(oldCity).trigger('change');
                        if (oldDistrict) {
                            setTimeout(function() {
                                districtSelect.val(oldDistrict);
                            }, 100);
                        }
                    }
                });
            // İl değişince ilçe selectbox'ını doldur
            citySelect.on('change', function() {
                const selectedCity = $(this).val();
                districtSelect.empty().append('<option value="">İlçe seçiniz</option>');
                if (selectedCity && cityDistricts[selectedCity]) {
                    cityDistricts[selectedCity].forEach(function(district) {
                        districtSelect.append(`<option value="${district}">${district}</option>`);
                    });
                    districtSelect.prop('disabled', false);
                } else {
                    districtSelect.prop('disabled', true);
                }
            });
            $(".inputmask").inputmask({
                mask: "+99 999 999 99 99",
                showMaskOnHover: false,
                showMaskOnFocus: true,
                clearIncomplete: true,
                definitions: {
                    '9': {
                        validator: "[0-9]",
                        cardinality: 1,
                        definitionSymbol: "9"
                    }
                },
                onBeforePaste: function (pastedValue, opts) {
                    return pastedValue.replace(/[^\d\+]/g, '');
                },
                onKeyDown: function(e, buffer, caretPos, opts) {
                    var key = e.key;
                    if (!/[0-9]/.test(key) && key.length === 1) {
                        e.preventDefault();
                    }
                }
            });

    // Yetkili kişi yönetimi
    let authorizedPersonIndex = 0;
    let companyPhoneIndex = 1; // İlk telefon zaten var, 1'den başla

    function updateRemoveButtons() {
        const items = $('.authorized-person-item');
        if (items.length <= 1) {
            $('.remove-authorized-person').hide();
        } else {
            $('.remove-authorized-person').show();
        }
    }

    function updatePersonNumbers() {
        $('.authorized-person-item').each(function(index) {
            $(this).find('h6').text('Yetkili Kişi #' + (index + 1));
        });
    }

    function updatePhoneRemoveButtons() {
        const items = $('.company-phone-item');
        if (items.length <= 1) {
            $('.remove-company-phone').hide();
        } else {
            $('.remove-company-phone').show();
        }
    }

    function updatePhoneNumbers() {
        $('.company-phone-item').each(function(index) {
            $(this).find('h6').text('Şirket Telefon #' + (index + 1));
            $(this).attr('data-index', index);

            // Input name'lerini güncelle
            $(this).find('input[name*="[phone]"]').attr('name', 'company_phones[' + index + '][phone]');
            $(this).find('select[name*="[type]"]').attr('name', 'company_phones[' + index + '][type]');
        });
    }

    $('#add-authorized-person').on('click', function() {
        const container = $('#authorized-persons-container');
        const newItem = `
            <div class="authorized-person-item border p-3 mb-3 rounded" data-index="${authorizedPersonIndex}">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Yetkili Kişi #${authorizedPersonIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-authorized-person">
                        <i class="fas fa-trash"></i> Kaldır
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Ünvan</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][title]" placeholder="Örn: Genel Müdür, Satış Müdürü">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Telefon</label>
                            <input type="text" class="form-control inputmask" name="authorized_persons[${authorizedPersonIndex}][phone]" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 5xx xxx xx xx">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili İsim</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][first_name]" placeholder="Ad">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Soyisim</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][last_name]" placeholder="Soyad">
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.append(newItem);

        // Yeni eklenen telefon alanına inputmask uygula
        container.find('.inputmask').last().inputmask({
            mask: "+99 999 999 99 99",
            showMaskOnHover: false,
            showMaskOnFocus: true,
            clearIncomplete: true
        });

        authorizedPersonIndex++;
        updateRemoveButtons();
    });

    $(document).on('click', '.remove-authorized-person', function() {
        $(this).closest('.authorized-person-item').remove();
        updateRemoveButtons();
        updatePersonNumbers();
    });

    // Şirket telefonu ekleme
    $('#add-company-phone').on('click', function() {
        const container = $('#company-phones-container');
        const newItem = `
            <div class="company-phone-item border p-3 mb-3 rounded" data-index="${companyPhoneIndex}">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Şirket Telefon #${companyPhoneIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-company-phone">
                        <i class="fas fa-trash"></i> Kaldır
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Telefon Numarası</label>
                            <input type="text" class="form-control inputmask" name="company_phones[${companyPhoneIndex}][phone]" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 2xx xxx xx xx">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Telefon Türü</label>
                            <select class="form-control" name="company_phones[${companyPhoneIndex}][type]">
                                <option value="Sabit">Sabit Hat</option>
                                <option value="Mobil">Mobil</option>
                                <option value="Fax">Fax</option>
                                <option value="Diğer">Diğer</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.append(newItem);

        // Yeni eklenen telefon alanına inputmask uygula
        container.find('.inputmask').last().inputmask({
            mask: "+99 999 999 99 99",
            showMaskOnHover: false,
            showMaskOnFocus: true,
            clearIncomplete: true
        });

        companyPhoneIndex++;
        updatePhoneRemoveButtons();
    });

    // Şirket telefonu kaldırma
    $(document).on('click', '.remove-company-phone', function() {
        $(this).closest('.company-phone-item').remove();
        updatePhoneRemoveButtons();
        updatePhoneNumbers();
    });

    // Sayfa yüklendiğinde remove butonlarını güncelle
    updateRemoveButtons();
    updatePhoneRemoveButtons();

    // Potansiyel müşteriden gelen yetkili kişileri otomatik doldur
    <?php if(isset($authorizedPersons) && count($authorizedPersons) > 0): ?>
        <?php $__currentLoopData = $authorizedPersons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $person): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($index > 0 || !empty($person['title']) || !empty($person['first_name']) || !empty($person['last_name']) || !empty($person['phone'])): ?>
                const authorizedPersonHtml<?php echo e($index); ?> = `
                    <div class="authorized-person-item border p-3 mb-3 rounded" data-index="<?php echo e($index); ?>">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Yetkili Kişi #<?php echo e($index + 1); ?></h6>
                            <button type="button" class="btn btn-sm btn-danger remove-authorized-person">
                                <i class="fas fa-trash"></i> Kaldır
                            </button>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Yetkili Ünvan</label>
                                    <input type="text" class="form-control" name="authorized_persons[<?php echo e($index); ?>][title]" value="<?php echo e($person['title'] ?? ''); ?>" placeholder="Örn: Genel Müdür, Satış Müdürü">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Yetkili Telefon</label>
                                    <input type="text" class="form-control inputmask" name="authorized_persons[<?php echo e($index); ?>][phone]" value="<?php echo e($person['phone'] ?? ''); ?>" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 5xx xxx xx xx">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Yetkili İsim</label>
                                    <input type="text" class="form-control" name="authorized_persons[<?php echo e($index); ?>][first_name]" value="<?php echo e($person['first_name'] ?? ''); ?>" placeholder="Ad">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Yetkili Soyisim</label>
                                    <input type="text" class="form-control" name="authorized_persons[<?php echo e($index); ?>][last_name]" value="<?php echo e($person['last_name'] ?? ''); ?>" placeholder="Soyad">
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                $('#authorized-persons-container').append(authorizedPersonHtml<?php echo e($index); ?>);
                authorizedPersonIndex = Math.max(authorizedPersonIndex, <?php echo e($index + 1); ?>);
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>

    // Potansiyel müşteriden gelen şirket telefonlarını otomatik doldur
    <?php if(isset($companyPhones) && count($companyPhones) > 0): ?>
        <?php $__currentLoopData = $companyPhones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $phone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($index > 0 && (!empty($phone['phone']))): ?>
                const companyPhoneHtml<?php echo e($index); ?> = `
                    <div class="company-phone-item border p-3 mb-3 rounded" data-index="<?php echo e($index); ?>">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Şirket Telefon #<?php echo e($index + 1); ?></h6>
                            <button type="button" class="btn btn-sm btn-danger remove-company-phone">
                                <i class="fas fa-trash"></i> Kaldır
                            </button>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Telefon Numarası</label>
                                    <input type="text" class="form-control inputmask" name="company_phones[<?php echo e($index); ?>][phone]" value="<?php echo e($phone['phone'] ?? ''); ?>" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 2xx xxx xx xx">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">Telefon Türü</label>
                                    <select class="form-control" name="company_phones[<?php echo e($index); ?>][type]">
                                        <option value="Sabit" <?php echo e(($phone['type'] ?? '') == 'Sabit' ? 'selected' : ''); ?>>Sabit Hat</option>
                                        <option value="Mobil" <?php echo e(($phone['type'] ?? '') == 'Mobil' ? 'selected' : ''); ?>>Mobil</option>
                                        <option value="Fax" <?php echo e(($phone['type'] ?? '') == 'Fax' ? 'selected' : ''); ?>>Fax</option>
                                        <option value="Diğer" <?php echo e(($phone['type'] ?? '') == 'Diğer' ? 'selected' : ''); ?>>Diğer</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                $('#company-phones-container').append(companyPhoneHtml<?php echo e($index); ?>);
                companyPhoneIndex = Math.max(companyPhoneIndex, <?php echo e($index + 1); ?>);
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>

    // Yeni eklenen alanlar için inputmask uygula
    $('.inputmask').inputmask({
        mask: "+99 999 999 99 99",
        showMaskOnHover: false,
        showMaskOnFocus: true,
        clearIncomplete: true
    });

    // Remove butonlarını güncelle
    updateRemoveButtons();
    updatePhoneRemoveButtons();

        });
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /var/www/crm.umram.online/resources/views/customers/create.blade.php ENDPATH**/ ?>